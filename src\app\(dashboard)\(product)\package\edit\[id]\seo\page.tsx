'use client';

import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { EditTabs } from '@/modules/product/component/edit-tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

import { IPackageSEO } from '@/types/package_';
import { useUpdatePackageSeo } from '@/modules/package-seo/mutations/use-update-seo';
import { useGetPackageSeoById } from '@/modules/package-seo/queries/use-get-seo';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';

const initialSEO: Omit<IPackageSEO, 'id' | 'createdAt' | 'updatedAt'> = {
    packageId: '',
    metaTitle: '',
    metaDescription: '',
    metaKeywords: [],
    canonicalUrl: '',
    ogTitle: '',
    ogDescription: '',
    ogImage: '',
    ogType: '',
    ogUrl: '',
    ogSiteName: '',
    twitterTitle: '',
    twitterDescription: '',
    twitterImage: '',
    twitterCard: '',
    robots: '',
    language: '',
    structuredData: '',
    sitemapPriority: '',
    changefreq: '',
    customMetaTags: '',
    published: false,
};

export default function EditPackageSEOPage() {
    const router = useRouter();
    const { id: packageId } = useParams() as { id: string };

    const { data, isLoading, isError } = useGetPackageSeoById(packageId);
    const updateMutation = useUpdatePackageSeo(packageId);

    const [seo, setSEO] = useState<Omit<IPackageSEO, 'id' | 'createdAt' | 'updatedAt'>>(initialSEO);
    const [seoId, setSeoId] = useState<string>('');
    const [keywordsInput, setKeywordsInput] = useState('');

    useEffect(() => {
        if (data?.data && data.data.id) {
            setSeoId(data.data.id);
            setSEO({
                ...initialSEO,
                ...data.data,
                published: !!data.data.published,
                packageId,
                metaKeywords: data.data.metaKeywords || [],
            });

            setKeywordsInput(data.data.metaKeywords ? data.data.metaKeywords.join(', ') : '');
        }
    }, [data, packageId]);

    const handleChange = (field: keyof typeof seo, value: string) => {
       setSEO((prev) => ({
  ...prev,
  packageId: packageId,  // explicitly set packageId here
  [field]: value,
}));

    };

    const handleBooleanChange = (field: keyof typeof seo, value: boolean) => {
        setSEO((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleSave = () => {
        let structuredDataObj = {};
        try {
            structuredDataObj = seo.structuredData ? JSON.parse(seo.structuredData) : {};
        } catch (error) {
            toast.error('Structured Data is not valid JSON');
            return;
        }

        const metaKeywordsArr = keywordsInput
            .split(',')
            .map((k) => k.trim())
            .filter(Boolean);

        updateMutation.mutate(
            { ...seo, structuredData: structuredDataObj, metaKeywords: metaKeywordsArr, id: packageId },
            {
                onSuccess: () => {
                    toast.success('Package SEO updated successfully');
                    router.push(`/package/edit/${packageId}/seo`);
                },
                onError: (error) => {
                    toast.error(`Failed to update SEO: ${error.message}`);
                },
            }
        );
    };



    if (isLoading) return <div>Loading SEO data...</div>;
    if (isError) return <div>Error loading SEO data.</div>;

    return (
        <div className="min-h-screen p-6 bg-gray-50">
            <EditTabs packageId={packageId} />
            <Card>
                <CardHeader>
                    <CardTitle>Edit Package SEO</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 max-w-3xl">
                    <div>
                        <Label className='mb-2'>Meta Title</Label>
                        <Input
                            value={seo.metaTitle ?? ''}
                            onChange={(e) => handleChange('metaTitle', e.target.value)}
                            placeholder="Meta Title"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Meta Description</Label>
                        <Input
                            value={seo.metaDescription ?? ''}
                            onChange={(e) => handleChange('metaDescription', e.target.value)}
                            placeholder="Meta Description"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Meta Keywords (comma separated)</Label>
                        <Input
                            value={keywordsInput ?? ''}
                            onChange={(e) => setKeywordsInput(e.target.value)}
                            placeholder="keyword1, keyword2"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Canonical URL</Label>
                        <Input
                            value={seo.canonicalUrl ?? ''}
                            onChange={(e) => handleChange('canonicalUrl', e.target.value)}
                            placeholder="https://example.com"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>OG Title</Label>
                        <Input
                            value={seo.ogTitle ?? ''}
                            onChange={(e) => handleChange('ogTitle', e.target.value)}
                            placeholder="OG Title"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>OG Description</Label>
                        <Input
                            value={seo.ogDescription ?? ''}
                            onChange={(e) => handleChange('ogDescription', e.target.value)}
                            placeholder="OG Description"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>OG Image URL</Label>
                        <Input
                            value={seo.ogImage ?? ''}
                            onChange={(e) => handleChange('ogImage', e.target.value)}
                            placeholder="https://example.com/image.jpg"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>OG Type</Label>
                        <Input
                            value={seo.ogType ?? ''}
                            onChange={(e) => handleChange('ogType', e.target.value)}
                            placeholder="OG Type"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>OG URL</Label>
                        <Input
                            value={seo.ogUrl ?? ''}
                            onChange={(e) => handleChange('ogUrl', e.target.value)}
                            placeholder="https://example.com"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>OG Site Name</Label>
                        <Input
                            value={seo.ogSiteName ?? ''}
                            onChange={(e) => handleChange('ogSiteName', e.target.value)}
                            placeholder="OG Site Name"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Twitter Title</Label>
                        <Input
                            value={seo.twitterTitle ?? ''}
                            onChange={(e) => handleChange('twitterTitle', e.target.value)}
                            placeholder="Twitter Title"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Twitter Description</Label>
                        <Input
                            value={seo.twitterDescription ?? ''}
                            onChange={(e) => handleChange('twitterDescription', e.target.value)}
                            placeholder="Twitter Description"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Twitter Image URL</Label>
                        <Input
                            value={seo.twitterImage ?? ''}
                            onChange={(e) => handleChange('twitterImage', e.target.value)}
                            placeholder="https://example.com/image.jpg"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Twitter Card</Label>
                        <Input
                            value={seo.twitterCard ?? ''}
                            onChange={(e) => handleChange('twitterCard', e.target.value)}
                            placeholder="Twitter Card"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Robots</Label>
                        <Input
                            value={seo.robots ?? ''}
                            onChange={(e) => handleChange('robots', e.target.value)}
                            placeholder="Robots"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Language</Label>
                        <Input
                            value={seo.language ?? ''}
                            onChange={(e) => handleChange('language', e.target.value)}
                            placeholder="Language"
                            className="border-black/20 rounded-none"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Structured Data</Label>
                        <Textarea
                            value={seo.structuredData ?? ''}
                            onChange={(e) => handleChange('structuredData', e.target.value)}
                            placeholder="Structured Data"
                            className="border-black/20 rounded-none"
                            rows={10}
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Sitemap Priority</Label>
                        <Input
                            value={seo.sitemapPriority ?? ''}
                            onChange={(e) => handleChange('sitemapPriority', e.target.value)}
                            className="border-black/20 rounded-none"
                            placeholder="Sitemap Priority"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Change Frequency</Label>
                        <Input
                            value={seo.changefreq ?? ''}
                            onChange={(e) => handleChange('changefreq', e.target.value)}
                            className="border-black/20 rounded-none"
                            placeholder="Change Frequency"
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Custom Meta Tags</Label>
                        <Textarea
                            value={seo.customMetaTags ?? ''}
                            onChange={(e) => handleChange('customMetaTags', e.target.value)}
                            placeholder="Custom Meta Tags"
                            className="border-black/20 rounded-none"
                            rows={10}
                        />
                    </div>
                    <div>
                        <Label className='mb-2'>Published</Label>
                        <Checkbox
                            checked={seo.published ?? false}
                            onCheckedChange={(e) => handleBooleanChange('published', e as boolean)}
                        />
                    </div>
                    <div className="flex gap-4 justify-end pt-4 border-t">
                        <Button
                            onClick={handleSave}
                        >
                            Save
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
